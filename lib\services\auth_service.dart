import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthService extends ChangeNotifier {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final SharedPreferences _prefs;

  Map<String, dynamic>? _currentUser;
  bool _isAuthenticated = false;

  AuthService(this._prefs);

  Map<String, dynamic>? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;

  Stream<Map<String, dynamic>?> get authStateChanges =>
      Stream.fromFuture(getUserData()).asBroadcastStream();

  // Inscription avec email et mot de passe
  Future<Map<String, dynamic>?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String displayName,
  }) async {
    try {
      // Vérifier si l'email est déjà utilisé
      final usersJson = _prefs.getString('users') ?? '{}';
      final users = Map<String, dynamic>.from(json.decode(usersJson));

      if (users.containsKey(email)) {
        throw 'Un compte existe déjà avec cette adresse email';
      }

      // Hasher le mot de passe
      final hashedPassword = _hashPassword(password);

      // Créer l'utilisateur
      final newUser = {
        'email': email,
        'password': hashedPassword,
        'displayName': displayName,
        'createdAt': DateTime.now().toIso8601String(),
        'totalQuizzes': 0,
        'bestScore': 0,
        'averageScore': 0.0,
      };

      // Sauvegarder l'utilisateur
      users[email] = newUser;
      await _prefs.setString('users', json.encode(users));

      // Connecter l'utilisateur
      _currentUser = newUser;
      _isAuthenticated = true;
      await _secureStorage.write(key: 'session', value: email);

      notifyListeners();
      return newUser;
    } catch (e) {
      throw e.toString();
    }
  }

  // Connexion avec email et mot de passe
  Future<Map<String, dynamic>?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final usersJson = _prefs.getString('users') ?? '{}';
      final users = Map<String, dynamic>.from(json.decode(usersJson));

      if (!users.containsKey(email)) {
        throw 'Aucun utilisateur trouvé avec cette adresse email';
      }

      final user = Map<String, dynamic>.from(users[email]);
      final hashedPassword = _hashPassword(password);

      if (user['password'] != hashedPassword) {
        throw 'Mot de passe incorrect';
      }

      _currentUser = user;
      _isAuthenticated = true;
      await _secureStorage.write(key: 'session', value: email);

      notifyListeners();
      return user;
    } catch (e) {
      throw e.toString();
    }
  }

  // Déconnexion
  Future<void> signOut() async {
    try {
      _currentUser = null;
      _isAuthenticated = false;
      await _secureStorage.delete(key: 'session');
      notifyListeners();
    } catch (e) {
      throw 'Erreur lors de la déconnexion';
    }
  }

  // Réinitialisation du mot de passe
  Future<void> resetPassword(String email) async {
    try {
      // Dans une vraie implémentation, vous enverriez un email avec un lien de réinitialisation
      // Ici, nous allons simplement simuler cette opération
      final usersJson = _prefs.getString('users') ?? '{}';
      final users = Map<String, dynamic>.from(json.decode(usersJson));

      if (!users.containsKey(email)) {
        throw 'Aucun utilisateur trouvé avec cette adresse email';
      }

      // En production, vous devriez générer un token et l'envoyer par email
      // avec un lien vers une page de réinitialisation
      return;
    } catch (e) {
      throw e.toString();
    }
  }

  // Obtenir les données utilisateur
  Future<Map<String, dynamic>?> getUserData() async {
    if (_currentUser != null) return _currentUser;

    try {
      final sessionEmail = await _secureStorage.read(key: 'session');
      if (sessionEmail == null) return null;

      final usersJson = _prefs.getString('users') ?? '{}';
      final users = Map<String, dynamic>.from(json.decode(usersJson));

      if (users.containsKey(sessionEmail)) {
        _currentUser = Map<String, dynamic>.from(users[sessionEmail]);
        _isAuthenticated = true;
        return _currentUser;
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la récupération des données utilisateur: $e');
      }
      return null;
    }
  }

  // Mettre à jour les statistiques utilisateur
  Future<void> updateUserStats({
    required int score,
    required int totalQuestions,
    required String category,
    required String difficulty,
  }) async {
    if (_currentUser == null) return;

    try {
      final usersJson = _prefs.getString('users') ?? '{}';
      final users = Map<String, dynamic>.from(json.decode(usersJson));
      final email = _currentUser!['email'];

      if (users.containsKey(email)) {
        final user = Map<String, dynamic>.from(users[email]);
        final currentTotalQuizzes = user['totalQuizzes'] ?? 0;
        final currentBestScore = user['bestScore'] ?? 0;
        final currentAverageScore = user['averageScore']?.toDouble() ?? 0.0;

        final double percentage = (score / totalQuestions) * 100;
        final int newTotalQuizzes = currentTotalQuizzes + 1;
        final int newBestScore =
            percentage > currentBestScore
                ? percentage.round()
                : currentBestScore;
        final double newAverageScore =
            ((currentAverageScore * currentTotalQuizzes) + percentage) /
            newTotalQuizzes;

        user['totalQuizzes'] = newTotalQuizzes;
        user['bestScore'] = newBestScore;
        user['averageScore'] = newAverageScore;
        user['lastQuizDate'] = DateTime.now().toIso8601String();

        // Sauvegarder les modifications
        users[email] = user;
        await _prefs.setString('users', json.encode(users));

        // Mettre à jour l'utilisateur courant
        _currentUser = user;

        // Ajouter le score individuel
        final scoresJson = _prefs.getString('user_scores_$email') ?? '[]';
        final scores = List<Map<String, dynamic>>.from(json.decode(scoresJson));

        scores.add({
          'score': score,
          'totalQuestions': totalQuestions,
          'percentage': percentage,
          'category': category,
          'difficulty': difficulty,
          'date': DateTime.now().toIso8601String(),
        });

        await _prefs.setString('user_scores_$email', json.encode(scores));

        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la mise à jour des statistiques: $e');
      }
    }
  }

  // Hasher le mot de passe (simplifié - en production, utilisez une meilleure méthode)
  String _hashPassword(String password) {
    final bytes = utf8.encode(
      password + 'salt',
    ); // Ajouter un salt en production
    return sha256.convert(bytes).toString();
  }
}
